/* Dark Cybersecurity Theme - Modern & Professional */

:root {
    /* Primary Colors */
    --primary-color: #00d4ff;
    --primary-dark: #0099cc;
    --primary-light: #33ddff;
    
    /* Secondary Colors */
    --secondary-color: #ff6b35;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --danger-color: #ff3366;
    --info-color: #00d4ff;
    
    /* Background Colors */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #252b3d;
    --bg-card: #1e2332;
    --bg-hover: #2a3142;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #7a8394;
    --text-accent: #00d4ff;
    
    /* Border Colors */
    --border-color: #2a3142;
    --border-accent: #00d4ff;
    
    /* Shadow Colors */
    --shadow-primary: rgba(0, 212, 255, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.5);
    
    /* Gradient Backgrounds */
    --gradient-primary: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
    --gradient-card: linear-gradient(145deg, #1e2332 0%, #252b3d 100%);
    --gradient-accent: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-danger: linear-gradient(135deg, #ff3366 0%, #cc1a4d 100%);
    --gradient-success: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
    --gradient-warning: linear-gradient(135deg, #ffaa00 0%, #cc8800 100%);
}

/* Global Styles */
body {
    background: var(--gradient-primary);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Navigation */
.navbar {
    background: rgba(26, 31, 46, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 0 10px var(--shadow-primary);
}

.navbar-brand i {
    color: var(--primary-color);
    filter: drop-shadow(0 0 5px var(--primary-color));
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    text-shadow: 0 0 10px var(--shadow-primary);
}

.navbar-nav .nav-link.active {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
}

/* Cards */
.card {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-accent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 20px var(--shadow-primary);
    border-color: var(--primary-color);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: rgba(0, 212, 255, 0.05);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
}

.card-header h5 {
    color: var(--text-accent);
    text-shadow: 0 0 10px var(--shadow-primary);
}

/* Stat Cards */
.stat-card {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    animation: rotate 8s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.stat-icon {
    background: var(--gradient-accent) !important;
    box-shadow: 0 0 20px var(--shadow-primary);
    animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
    from { box-shadow: 0 0 20px var(--shadow-primary); }
    to { box-shadow: 0 0 30px var(--shadow-primary), 0 0 40px rgba(0, 212, 255, 0.3); }
}

/* Buttons */
.btn {
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-accent);
    border: none;
    box-shadow: 0 4px 15px var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-primary);
}

.btn-success {
    background: var(--gradient-success);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}

.btn-warning {
    background: var(--gradient-warning);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 170, 0, 0.3);
}

.btn-danger {
    background: var(--gradient-danger);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 51, 102, 0.3);
}

/* Forms */
.form-control {
    background: rgba(37, 43, 61, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(37, 43, 61, 1);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem var(--shadow-primary);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Tables */
.table {
    color: var(--text-primary);
    border-radius: 16px;
    overflow: hidden;
}

.table thead th {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--border-color);
    color: var(--text-accent);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    border-color: var(--border-color);
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(0, 212, 255, 0.05);
    transform: scale(1.01);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 12px;
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.alert-success {
    background: rgba(0, 255, 136, 0.1);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-warning {
    background: rgba(255, 170, 0, 0.1);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: rgba(255, 51, 102, 0.1);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.alert-info {
    background: rgba(0, 212, 255, 0.1);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

/* Badges */
.badge {
    font-weight: 600;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading States */
.spinner-border {
    border-color: var(--primary-color);
    border-right-color: transparent;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-light);
}

/* Connection Status */
#connection-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        border-radius: 12px;
    }
    
    .btn {
        border-radius: 8px;
    }
    
    .form-control {
        border-radius: 8px;
    }
}
