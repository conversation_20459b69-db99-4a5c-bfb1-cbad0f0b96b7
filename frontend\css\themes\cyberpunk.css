/* Cyberpunk Theme - Futuristic & Neon */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

:root {
    /* Neon Colors */
    --neon-pink: #ff0080;
    --neon-cyan: #00ffff;
    --neon-green: #00ff41;
    --neon-purple: #8a2be2;
    --neon-orange: #ff4500;
    --neon-yellow: #ffff00;
    
    /* Primary Colors */
    --primary-color: #00ffff;
    --primary-dark: #00cccc;
    --primary-light: #33ffff;
    
    /* Secondary Colors */
    --secondary-color: #ff0080;
    --success-color: #00ff41;
    --warning-color: #ffff00;
    --danger-color: #ff0040;
    --info-color: #00ffff;
    
    /* Background Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a0a1a;
    --bg-tertiary: #2a1a2a;
    --bg-card: #1a1a2e;
    --bg-hover: #2a2a4e;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --text-accent: #00ffff;
    
    /* Border Colors */
    --border-color: #333366;
    --border-accent: #00ffff;
    
    /* Shadow Colors */
    --shadow-neon-cyan: 0 0 20px #00ffff;
    --shadow-neon-pink: 0 0 20px #ff0080;
    --shadow-neon-green: 0 0 20px #00ff41;
    --shadow-dark: rgba(0, 0, 0, 0.8);
}

/* Global Styles */
body {
    background: 
        radial-gradient(circle at 25% 25%, #1a0a1a 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, #0a1a1a 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a0a0a 100%);
    color: var(--text-primary);
    font-family: 'Orbitron', 'Courier New', monospace;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0, 255, 255, 0.03) 2px,
            rgba(0, 255, 255, 0.03) 4px
        );
    pointer-events: none;
    z-index: -1;
    animation: scanlines 0.1s linear infinite;
}

@keyframes scanlines {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

/* Glitch Effect */
.glitch {
    position: relative;
    animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--neon-pink);
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--neon-cyan);
    z-index: -2;
}

@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-2px, -2px); }
    20% { transform: translate(2px, 2px); }
    30% { transform: translate(-2px, 2px); }
    40% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(2px, 2px); }
    20% { transform: translate(-2px, -2px); }
    30% { transform: translate(2px, -2px); }
    40% { transform: translate(-2px, 2px); }
}

/* Navigation */
.navbar {
    background: rgba(26, 10, 26, 0.9) !important;
    backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--neon-cyan);
    box-shadow: var(--shadow-neon-cyan);
}

.navbar-brand {
    color: var(--neon-cyan) !important;
    font-weight: 900;
    font-size: 1.8rem;
    text-shadow: var(--shadow-neon-cyan);
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.navbar-brand i {
    color: var(--neon-green);
    filter: drop-shadow(var(--shadow-neon-green));
    animation: pulse-green 2s ease-in-out infinite alternate;
}

@keyframes pulse-green {
    from { filter: drop-shadow(0 0 5px #00ff41); }
    to { filter: drop-shadow(0 0 20px #00ff41); }
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    font-family: 'Orbitron', monospace;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--neon-pink);
    transition: width 0.3s ease;
    box-shadow: var(--shadow-neon-pink);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--neon-pink) !important;
    text-shadow: var(--shadow-neon-pink);
}

/* Cards */
.card {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid var(--neon-cyan);
    border-radius: 0;
    box-shadow: 
        var(--shadow-neon-cyan),
        inset 0 1px 0 rgba(0, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink), var(--neon-green), var(--neon-cyan));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: border-glow 3s linear infinite;
}

@keyframes border-glow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.card:hover::before {
    opacity: 1;
}

.card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 
        0 0 30px var(--neon-cyan),
        0 0 60px rgba(0, 255, 255, 0.3);
}

.card-header {
    background: rgba(0, 255, 255, 0.1);
    border-bottom: 1px solid var(--neon-cyan);
    color: var(--neon-cyan);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Orbitron', monospace;
}

/* Stat Cards */
.stat-card {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid var(--neon-green);
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.2), transparent);
    animation: scan 3s ease-in-out infinite;
}

@keyframes scan {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 0 40px var(--neon-green);
    border-color: var(--neon-pink);
}

.stat-icon {
    background: linear-gradient(135deg, var(--neon-cyan) 0%, var(--neon-pink) 100%) !important;
    box-shadow: var(--shadow-neon-cyan);
    animation: icon-pulse 2s ease-in-out infinite alternate;
    border: 2px solid var(--neon-cyan);
}

@keyframes icon-pulse {
    from { 
        box-shadow: 0 0 10px var(--neon-cyan);
        transform: scale(1);
    }
    to { 
        box-shadow: 0 0 30px var(--neon-cyan), 0 0 40px rgba(0, 255, 255, 0.5);
        transform: scale(1.1);
    }
}

/* Buttons */
.btn {
    font-weight: 700;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Orbitron', monospace;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    border-color: var(--neon-cyan);
    color: var(--neon-cyan);
    box-shadow: var(--shadow-neon-cyan);
}

.btn-primary:hover {
    background: var(--neon-cyan);
    color: #000000;
    box-shadow: 0 0 30px var(--neon-cyan);
    transform: translateY(-2px);
}

.btn-success {
    border-color: var(--neon-green);
    color: var(--neon-green);
    box-shadow: var(--shadow-neon-green);
}

.btn-success:hover {
    background: var(--neon-green);
    color: #000000;
    box-shadow: 0 0 30px var(--neon-green);
}

.btn-warning {
    border-color: var(--neon-yellow);
    color: var(--neon-yellow);
    box-shadow: 0 0 20px var(--neon-yellow);
}

.btn-danger {
    border-color: var(--neon-pink);
    color: var(--neon-pink);
    box-shadow: var(--shadow-neon-pink);
}

/* Forms */
.form-control {
    background: rgba(26, 26, 46, 0.8);
    border: 2px solid var(--neon-cyan);
    color: var(--text-primary);
    font-family: 'Orbitron', monospace;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(26, 26, 46, 1);
    border-color: var(--neon-pink);
    box-shadow: var(--shadow-neon-pink);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
    font-family: 'Orbitron', monospace;
}

.form-label {
    color: var(--neon-cyan);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Orbitron', monospace;
}

/* Tables */
.table {
    color: var(--text-primary);
    font-family: 'Orbitron', monospace;
}

.table thead th {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--neon-cyan);
    color: var(--neon-cyan);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table tbody tr {
    border-color: var(--border-color);
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(0, 255, 255, 0.05);
    box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.1);
}

/* Alerts */
.alert {
    border: 2px solid;
    background: transparent;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.alert-success {
    border-color: var(--neon-green);
    color: var(--neon-green);
    box-shadow: var(--shadow-neon-green);
}

.alert-warning {
    border-color: var(--neon-yellow);
    color: var(--neon-yellow);
    box-shadow: 0 0 20px var(--neon-yellow);
}

.alert-danger {
    border-color: var(--neon-pink);
    color: var(--neon-pink);
    box-shadow: var(--shadow-neon-pink);
}

.alert-info {
    border-color: var(--neon-cyan);
    color: var(--neon-cyan);
    box-shadow: var(--shadow-neon-cyan);
}

/* Badges */
.badge {
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Orbitron', monospace;
    border: 1px solid;
}

/* Loading States */
.spinner-border {
    border-color: var(--neon-cyan);
    border-right-color: transparent;
    animation: spin 1s linear infinite, glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 5px var(--neon-cyan)); }
    to { filter: drop-shadow(0 0 20px var(--neon-cyan)); }
}

/* Connection Status */
#connection-status {
    background: rgba(0, 255, 65, 0.1);
    border: 1px solid var(--neon-green);
    color: var(--neon-green);
    box-shadow: var(--shadow-neon-green);
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border: 1px solid var(--neon-cyan);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--neon-cyan) 0%, var(--neon-pink) 100%);
    border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
    box-shadow: var(--shadow-neon-cyan);
}

/* Matrix Rain Effect */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -2;
    opacity: 0.1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.4rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}
